/*
=======================================================
= File: gmx_online_mode.dart
= Project: LavaMail
= Description:
=   - GMX API service for online mode using IMAP/SMTP
=   - Implements email operations through direct IMAP/SMTP connection
=   - All code, comments, and documentation are in English as per project standards.
=======================================================
*/

import 'dart:async';
import 'package:logger/logger.dart';
import 'package:enough_mail/enough_mail.dart';
import '../models/gmx_user.dart';
import 'gmx_imap_client.dart';

// =======================================================
// = Class: GmxApiService
// = Description: GMX API service using IMAP/SMTP
// =======================================================
class GmxApiService {
  final GmxUser? user;
  final Logger _logger = Logger();
  late GmxImapClient _imapClient;
  bool _initialized = false;

  GmxApiService(this.user);

  // =======================================================
  // = Function: _ensureInitialized
  // = Description: Ensures the service is initialized
  // =======================================================
  Future<void> _ensureInitialized() async {
    if (!_initialized) {
      if (user == null) {
        throw Exception('No GMX user provided');
      }
      _imapClient = GmxImapClient();
      await _imapClient.connect();
      _initialized = true;
      _logger.i('GMX API service initialized for: ${user!.email}');
    }
  }

  // =======================================================
  // = Function: getCategoryStats
  // = Description: Gets statistics for email categories with real GMX folder names
  // =======================================================
  Future<Map<String, dynamic>> getCategoryStats(String category) async {
    try {
      await _ensureInitialized();
      _logger.d('Getting GMX stats for category: $category');
      final mailboxName = _resolveGmxFolderName(category);
      final stats = await _getMailboxStats(mailboxName);
      _logger.d('GMX stats for $category ($mailboxName): $stats');
      return stats;
    } catch (e, stack) {
      _logger.e('Error getting GMX category stats for $category', error: e, stackTrace: stack);
      return {
        'count': 0,
        'unread': 0,
        'size': 0,
      };
    }
  }

  // =======================================================
  // = Function: getAllFolders
  // = Description: Gets all available folders with their statistics
  // =======================================================
  Future<List<Map<String, dynamic>>> getAllFolders() async {
    try {
      await _ensureInitialized();
      _logger.d('Getting all GMX folders');

      final mailboxes = await _imapClient.getMailboxes();
      final folders = <Map<String, dynamic>>[];

      for (final mailbox in mailboxes) {
        // Try to get real stats for all folders
        try {
          final stats = await _getMailboxStats(mailbox.name);
          folders.add({
            'id': mailbox.name,
            'name': mailbox.name,
            'displayName': _getFolderDisplayName(mailbox.name),
            'path': mailbox.path,
            'count': stats['count'],
            'unread': stats['unread'],
            'size': stats['size'],
            'isSelectable': true,
            'hasChildren': mailbox.hasChildren,
          });
        } catch (e) {
          _logger.w('Error getting stats for ${mailbox.name}: $e');
          folders.add({
            'id': mailbox.name,
            'name': mailbox.name,
            'displayName': _getFolderDisplayName(mailbox.name),
            'path': mailbox.path,
            'count': 0,
            'unread': 0,
            'size': 0,
            'isSelectable': false, // Mark as not selectable if stats failed
            'hasChildren': mailbox.hasChildren,
          });
        }
      }

      _logger.i('Found ${folders.length} GMX folders');
      return folders;
    } catch (e, stack) {
      _logger.e('Error getting GMX folders', error: e, stackTrace: stack);
      return [];
    }
  }

  /// Get statistics for a specific mailbox
  Future<Map<String, dynamic>> _getMailboxStats(String mailboxName) async {
    try {
      final mailbox = await _imapClient.getMailboxStatus(mailboxName);

      if (mailbox == null) {
        return {
          'count': 0,
          'unread': 0,
          'size': 0,
        };
      }

      return {
        'count': mailbox.messagesExists,
        'unread': mailbox.messagesUnseen,
        'size': 0, // GMX doesn't provide size info easily via IMAP
      };
    } catch (e) {
      _logger.w('Error getting stats for mailbox $mailboxName: $e');
      return {
        'count': 0,
        'unread': 0,
        'size': 0,
      };
    }
  }

  /// Get display name for folder
  String _getFolderDisplayName(String folderName) {
    switch (folderName.toLowerCase()) {
      case 'inbox':
        return 'Courrier reçu';
      case 'sent':
      case 'gesendet':
      case 'envoyés':
        return 'Envoyés';
      case 'spam':
      case 'junk':
      case 'spamverdacht':
        return 'Spam';
      case 'trash':
      case 'deleted':
      case 'papierkorb':
      case 'corbeille':
        return 'Corbeille';
      case 'drafts':
      case 'entwürfe':
      case 'brouillons':
        return 'Brouillons';
      default:
        return folderName;
    }
  }

  // =======================================================
  // = Function: getEmails
  // = Description: Retrieves emails with optional query parameters and metadata
  // =======================================================
  Future<Map<String, dynamic>> getEmails({
    String? q,
    int? maxResults,
    String? pageToken,
    bool includeMetadata = true,
  }) async {
    try {
      await _ensureInitialized();
      _logger.d('Fetching GMX emails with query: $q');

      List<MimeMessage> messages;

      if (q != null && q.isNotEmpty) {
        messages = await _imapClient.searchMessages(
          mailboxName: 'INBOX',
          query: q,
          limit: maxResults ?? 50,
        );
      } else {
        messages = await _imapClient.fetchMessages(
          mailboxName: 'INBOX',
          limit: maxResults ?? 50,
          fetchStructure: includeMetadata,
        );
      }

      final emailList = <Map<String, dynamic>>[];
      for (final message in messages) {
        emailList.add(_convertMessageToMap(message));
      }

      return {
        'messages': emailList,
        'nextPageToken': null, // GMX doesn't support pagination like Gmail
        'resultSizeEstimate': emailList.length,
      };
    } catch (e, stack) {
      _logger.e('Error fetching GMX emails', error: e, stackTrace: stack);
      return {
        'messages': <Map<String, dynamic>>[],
        'nextPageToken': null,
        'resultSizeEstimate': 0,
      };
    }
  }

  // =======================================================
  // = Function: _convertMessageToMap
  // = Description: Converts MimeMessage to Map format compatible with Gmail API
  // =======================================================
  Map<String, dynamic> _convertMessageToMap(MimeMessage message) {
    try {
      return {
        'id': message.uid?.toString() ?? message.sequenceId?.toString() ?? 'unknown',
        'threadId': message.uid?.toString() ?? message.sequenceId?.toString() ?? 'unknown',
        'labelIds': _extractLabels(message),
        'snippet': _extractSnippet(message),
        'payload': _extractPayload(message),
        'sizeEstimate': message.size ?? 0,
        'historyId': message.uid?.toString(),
        'internalDate': message.decodeDate()?.millisecondsSinceEpoch.toString(),
      };
    } catch (e) {
      _logger.w('Error converting message to map: $e');
      return {
        'id': 'error',
        'threadId': 'error',
        'labelIds': <String>[],
        'snippet': 'Error loading message',
        'payload': {},
        'sizeEstimate': 0,
      };
    }
  }

  List<String> _extractLabels(MimeMessage message) {
    final labels = <String>['INBOX'];
    
    if (!message.isSeen) {
      labels.add('UNREAD');
    }
    
    if (message.isFlagged) {
      labels.add('STARRED');
    }
    
    return labels;
  }

  String _extractSnippet(MimeMessage message) {
    try {
      final text = message.decodeTextPlainPart() ??
                   message.decodeTextHtmlPart() ??
                   'No preview available';
      return text.length > 150 ? '${text.substring(0, 150)}...' : text;
    } catch (e) {
      return 'No preview available';
    }
  }

  Map<String, dynamic> _extractPayload(MimeMessage message) {
    try {
      final headers = <String, String>{};
      final messageHeaders = message.headers;
      if (messageHeaders != null && messageHeaders.isNotEmpty) {
        for (final header in messageHeaders) {
          headers[header.name] = header.value.toString();
        }
      }

      return {
        'headers': headers,
        'body': {
          'size': message.size ?? 0,
        },
        'filename': message.decodeSubject() ?? 'No subject',
        'mimeType': message.mediaType.toString(),
      };
    } catch (e) {
      return {
        'headers': <String, String>{},
        'body': {'size': 0},
        'filename': 'No subject',
        'mimeType': 'text/plain',
      };
    }
  }

  bool _hasAttachments(MimeMessage message) {
    try {
      final attachmentInfo = message.findContentInfo(disposition: ContentDisposition.attachment);
      return attachmentInfo.isNotEmpty;
    } catch (e) {
      return false;
    }
  }

  int _countAttachments(MimeMessage message) {
    try {
      // Count all parts with attachment disposition
      int count = 0;
      for (final part in message.allPartsFlat) {
        if (part.getHeaderContentDisposition()?.disposition == ContentDisposition.attachment) {
          count++;
        }
      }
      return count;
    } catch (e) {
      return 0;
    }
  }

  // =======================================================
  // = Function: getAttachmentAnalysis
  // = Description: Gets attachment analysis (count, emails with attachments, etc.)
  // =======================================================
  Future<Map<String, dynamic>> getAttachmentAnalysis({int maxSample = 50}) async {
    try {
      await _ensureInitialized();
      _logger.d('Analyzing GMX attachments');

      final messages = await _imapClient.fetchMessages(
        mailboxName: 'INBOX',
        limit: maxSample,
        fetchStructure: true,
      );

      int attachmentCount = 0;
      int emailsWithAttachments = 0;

      for (final message in messages) {
        final hasAttachments = _hasAttachments(message);
        if (hasAttachments) {
          emailsWithAttachments++;
          attachmentCount += _countAttachments(message);
        }
      }

      final result = {
        'hasAttachments': attachmentCount > 0,
        'totalAttachments': attachmentCount,
        'emailsWithAttachments': emailsWithAttachments,
        'sampleSize': messages.length,
      };

      _logger.i('GMX attachment analysis: $result');
      return result;
    } catch (e, stack) {
      _logger.e('Error analyzing GMX attachments', error: e, stackTrace: stack);
      return {
        'hasAttachments': false,
        'totalAttachments': 0,
        'emailsWithAttachments': 0,
        'sampleSize': 0,
      };
    }
  }

  // =======================================================
  // = Function: getTotalEmailCount
  // = Description: Gets total email count across all folders
  // =======================================================
  Future<int> getTotalEmailCount() async {
    try {
      await _ensureInitialized();
      _logger.d('Getting total GMX email count (all folders)');
      final mailboxes = await _imapClient.getMailboxes();
      int totalCount = 0;
      for (final mailbox in mailboxes) {
        try {
          final stats = await _getMailboxStats(mailbox.name);
          totalCount += ((stats['count'] ?? 0) as num).toInt();
        } catch (e) {
          _logger.w('Error getting count for folder ${mailbox.name}: $e');
        }
      }
      _logger.i('Total GMX emails (all folders): $totalCount');
      return totalCount;
    } catch (e, stack) {
      _logger.e('Error getting total GMX email count', error: e, stackTrace: stack);
      return 0;
    }
  }

  // =======================================================
  // = Function: getInboxStats
  // = Description: Gets inbox statistics (for compatibility with Gmail API)
  // =======================================================
  Future<Map<String, dynamic>> getInboxStats() async {
    return await getCategoryStats('inbox');
  }

  // =======================================================
  // = Function: getSpamStats
  // = Description: Gets spam statistics (for compatibility with Gmail API)
  // =======================================================
  Future<Map<String, dynamic>> getSpamStats() async {
    return await getCategoryStats('spam');
  }

  // =======================================================
  // = Function: dispose
  // = Description: Cleans up resources
  // =======================================================
  Future<void> dispose() async {
    try {
      if (_initialized) {
        await _imapClient.disconnect();
        _initialized = false;
        _logger.i('GMX API service disposed');
      }
    } catch (e) {
      _logger.e('Error disposing GMX API service', error: e);
    }
  }

  // Helper: Map standard category to GMX folder name
  String _resolveGmxFolderName(String category) {
    switch (category.toLowerCase()) {
      case 'inbox':
      case 'primary':
      case 'courrier_recu':
        return 'INBOX';
      case 'sent':
      case 'envoyes':
      case 'gesendet':
        return 'Sent';
      case 'spam':
      case 'junk':
      case 'spamverdacht':
        return 'Spam';
      case 'trash':
      case 'deleted':
      case 'papierkorb':
      case 'corbeille':
        return 'Trash';
      case 'drafts':
      case 'entwürfe':
      case 'brouillons':
        return 'Drafts';
      default:
        return category;
    }
  }
}
