/*
=======================================================
= File: icloud_login_modal.dart
= Project: LavaMail
= Description:
=   - iCloud login modal for email authentication
=   - Offers a selector and then the iCloud login form
=   - All code, comments, and documentation are in English as per project standards.
=======================================================
*/

import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:logger/logger.dart';
import '../../../core/icloud/auth/icloud_auth_service.dart';
import '../../../providers/user_provider.dart';
import '../../home_screen/home_screen.dart';
import '../../theme/widgets/common_widgets.dart';

class IcloudLoginModal extends StatefulWidget {
  final VoidCallback? onBack;

  const IcloudLoginModal({super.key, this.onBack});

  @override
  State<IcloudLoginModal> createState() => _IcloudLoginModalState();
}

class _IcloudLoginModalState extends State<IcloudLoginModal> {
  @override
  Widget build(BuildContext context) {
    return LavaMailModal(
      title: 'Sign in to iCloud',
      titleIcon: Icons.cloud,
      content: IcloudLoginForm(
        onBack: () {
          if (widget.onBack != null) {
            widget.onBack!();
          } else {
            Navigator.of(context).pop();
          }
        },
      ),
      actions: [
        LavaMailOutlinedButton(
          text: 'Cancel',
          onPressed: () {
            if (widget.onBack != null) {
              widget.onBack!();
            } else {
              Navigator.of(context).pop();
            }
          },
        ),
      ],
    );
  }
}

class IcloudLoginForm extends StatefulWidget {
  final VoidCallback onBack;
  const IcloudLoginForm({super.key, required this.onBack});

  @override
  State<IcloudLoginForm> createState() => _IcloudLoginFormState();
}

class _IcloudLoginFormState extends State<IcloudLoginForm> {
  final Logger _logger = Logger();
  final _formKey = GlobalKey<FormState>();
  final _emailController = TextEditingController();
  final _appPasswordController = TextEditingController();
  final _imapHostController = TextEditingController(text: 'imap.mail.me.com');
  final _imapPortController = TextEditingController(text: '993');
  final _smtpHostController = TextEditingController(text: 'smtp.mail.me.com');
  final _smtpPortController = TextEditingController(text: '587');
  bool _isLoading = false;
  bool _useSSL = true;
  bool _showAdvancedSettings = false;
  bool _obscurePassword = true;
  bool _hasCredentialsSaved = false;
  bool _showDirectConnect = false;

  @override
  void initState() {
    super.initState();
    _loadSavedCredentials();
  }

  @override
  void dispose() {
    _emailController.dispose();
    _appPasswordController.dispose();
    _imapHostController.dispose();
    _imapPortController.dispose();
    _smtpHostController.dispose();
    _smtpPortController.dispose();
    super.dispose();
  }

  /// Loads saved iCloud credentials and pre-fills the form
  Future<void> _loadSavedCredentials() async {
    try {
      final savedUser = await IcloudAuthService.loadSavedUser();
      if (savedUser != null && mounted) {
        setState(() {
          _hasCredentialsSaved = true;
          _showDirectConnect = true;
          // Pre-fill email (visible) and password (hidden)
          _emailController.text = savedUser.email;
          _appPasswordController.text = savedUser.appPassword;
          _imapHostController.text = savedUser.imapHost;
          _imapPortController.text = savedUser.imapPort.toString();
          _smtpHostController.text = savedUser.smtpHost;
          _smtpPortController.text = savedUser.smtpPort.toString();
          _useSSL = savedUser.useSSL;
        });
        _logger.i('iCloud credentials loaded and form pre-filled for: ${savedUser.email}');
      }
    } catch (e) {
      _logger.w('Failed to load saved iCloud credentials: $e');
    }
  }

  Future<void> _handleLogin() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }
    setState(() { _isLoading = true; });
    try {
      _logger.d('Attempting iCloud login');
      final user = await IcloudAuthService.signInWithCredentials(
        email: _emailController.text.trim(),
        appPassword: _appPasswordController.text,
        imapHost: _imapHostController.text.trim(),
        imapPort: int.parse(_imapPortController.text),
        smtpHost: _smtpHostController.text.trim(),
        smtpPort: int.parse(_smtpPortController.text),
        useSSL: _useSSL,
      );
      if (user != null) {
        if (mounted) {
          final userProvider = Provider.of<UserProvider>(context, listen: false);
          userProvider.userType = UserType.icloud;
          userProvider.setIcloudUser(user);

          // Start data loading process as per Mermaid diagram
          await _startDataLoading(user);

          if (mounted) {
            Navigator.of(context).pushReplacement(
              MaterialPageRoute(builder: (context) => const HomeScreen()),
            );
          }
        }
      }
    } catch (e) {
      _logger.e('iCloud login failed', error: e);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Login failed: ${e.toString()}'),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 5),
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() { _isLoading = false; });
      }
    }
  }

  /// Handles direct connection with saved credentials
  Future<void> _handleDirectConnect() async {
    if (_emailController.text.isEmpty || _appPasswordController.text.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('No saved credentials found'),
          backgroundColor: Colors.orange,
        ),
      );
      return;
    }
    await _handleLogin();
  }

  /// Starts the data loading process as defined in the Mermaid diagram
  Future<void> _startDataLoading(dynamic user) async {
    try {
      _logger.i('Starting iCloud data loading process...');

      // Step 1: Load metadata (placeholder for now)
      _logger.d('Loading iCloud metadata...');

      // Step 2: Load folders/labels (placeholder for now)
      _logger.d('Loading iCloud folders/labels...');

      // Step 3: Count attachments (placeholder for now)
      _logger.d('Counting iCloud attachments...');

      _logger.i('iCloud data loading completed');
    } catch (e) {
      _logger.w('Error during iCloud data loading: $e');
      // Continue to home screen even if data loading fails
    }
  }

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          Row(
            children: [
              IconButton(
                onPressed: widget.onBack,
                icon: const Icon(Icons.arrow_back),
              ),
              const SizedBox(width: 8),
              const Icon(Icons.cloud, color: Colors.blue, size: 32),
              const SizedBox(width: 12),
              const Text(
                'iCloud Login',
                style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
              ),
            ],
          ),
          const SizedBox(height: 24),
          // Show direct connect button if credentials are saved
          if (_showDirectConnect && _hasCredentialsSaved) ...[
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.blue.shade50,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.blue.shade200),
              ),
              child: Column(
                children: [
                  Row(
                    children: [
                      Icon(Icons.check_circle, color: Colors.blue.shade700, size: 20),
                      const SizedBox(width: 8),
                      Text(
                        'Saved credentials found',
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          color: Colors.blue.shade700,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'Email: ${_emailController.text}',
                    style: const TextStyle(fontSize: 14),
                  ),
                  const SizedBox(height: 12),
                  ElevatedButton.icon(
                    onPressed: _isLoading ? null : _handleDirectConnect,
                    icon: const Icon(Icons.cloud),
                    label: const Text('Connect with saved credentials'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.blue,
                      foregroundColor: Colors.white,
                      minimumSize: const Size(double.infinity, 45),
                    ),
                  ),
                  const SizedBox(height: 8),
                  TextButton(
                    onPressed: () {
                      setState(() {
                        _showDirectConnect = false;
                      });
                    },
                    child: const Text('Use different credentials'),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 16),
          ],

          // Show form if no direct connect or user chose different credentials
          if (!_showDirectConnect || !_hasCredentialsSaved)
          Form(
            key: _formKey,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                TextFormField(
                  controller: _emailController,
                  decoration: InputDecoration(
                    labelText: 'Email Address',
                    hintText: '<EMAIL>',
                    prefixIcon: const Icon(Icons.email_outlined),
                    border: const OutlineInputBorder(),
                    filled: _hasCredentialsSaved,
                    fillColor: _hasCredentialsSaved ? Colors.blue.shade50 : null,
                  ),
                  keyboardType: TextInputType.emailAddress,
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Please enter your email address';
                    }
                    if (!value.contains('@')) {
                      return 'Please enter a valid email address';
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 16),
                TextFormField(
                  controller: _appPasswordController,
                  decoration: InputDecoration(
                    labelText: 'App Password',
                    hintText: _hasCredentialsSaved ? '••••••••••••••••' : 'Your iCloud app password',
                    prefixIcon: const Icon(Icons.lock_outlined),
                    suffixIcon: IconButton(
                      onPressed: () {
                        setState(() {
                          _obscurePassword = !_obscurePassword;
                        });
                      },
                      icon: Icon(_obscurePassword ? Icons.visibility : Icons.visibility_off),
                    ),
                    border: const OutlineInputBorder(),
                    filled: _hasCredentialsSaved,
                    fillColor: _hasCredentialsSaved ? Colors.blue.shade50 : null,
                  ),
                  obscureText: _obscurePassword,
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Please enter your app password';
                    }
                    if (value.length < 16) {
                      return 'iCloud app passwords are typically 16 characters';
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 8),
                TextButton(
                  onPressed: () {
                    setState(() {
                      _showAdvancedSettings = !_showAdvancedSettings;
                    });
                  },
                  child: Text(_showAdvancedSettings ? 'Hide Advanced Settings' : 'Show Advanced Settings'),
                ),
                if (_showAdvancedSettings) ...[
                  const SizedBox(height: 8),
                  TextFormField(
                    controller: _imapHostController,
                    decoration: const InputDecoration(
                      labelText: 'IMAP Host',
                      border: OutlineInputBorder(),
                    ),
                  ),
                  const SizedBox(height: 8),
                  TextFormField(
                    controller: _imapPortController,
                    decoration: const InputDecoration(
                      labelText: 'IMAP Port',
                      border: OutlineInputBorder(),
                    ),
                    keyboardType: TextInputType.number,
                  ),
                  const SizedBox(height: 8),
                  TextFormField(
                    controller: _smtpHostController,
                    decoration: const InputDecoration(
                      labelText: 'SMTP Host',
                      border: OutlineInputBorder(),
                    ),
                  ),
                  const SizedBox(height: 8),
                  TextFormField(
                    controller: _smtpPortController,
                    decoration: const InputDecoration(
                      labelText: 'SMTP Port',
                      border: OutlineInputBorder(),
                    ),
                    keyboardType: TextInputType.number,
                  ),
                  const SizedBox(height: 8),
                  SwitchListTile(
                    title: const Text('Use SSL'),
                    value: _useSSL,
                    onChanged: (v) => setState(() => _useSSL = v),
                  ),
                ],
                const SizedBox(height: 16),
                ElevatedButton(
                  onPressed: _isLoading ? null : _handleLogin,
                  style: ElevatedButton.styleFrom(
                    padding: const EdgeInsets.symmetric(vertical: 16),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                  child: _isLoading
                      ? const SizedBox(
                          height: 20,
                          width: 20,
                          child: CircularProgressIndicator(strokeWidth: 2),
                        )
                      : const Text(
                          'Login to iCloud',
                          style: TextStyle(fontSize: 16),
                        ),
                ),
                const SizedBox(height: 16),
                const Text(
                  'Important: You must use an iCloud app password (not your main Apple ID password).\nGenerate one in your Apple ID security settings.\n\nDefault settings:\n• IMAP: imap.mail.me.com:993 (SSL)\n• SMTP: smtp.mail.me.com:587 (SSL)',
                  style: TextStyle(fontSize: 11, color: Colors.grey),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
} 