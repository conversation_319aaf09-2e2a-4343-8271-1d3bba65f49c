import 'package:flutter/material.dart';
import 'package:google_sign_in/google_sign_in.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:provider/provider.dart';

// Core imports
import '../../core/gmail/offline_mode/gmail_offline_mode.dart';
import '../../core/gmail/common_functions/common_functions.dart';

// Settings imports
import '../../utils/app_settings.dart';
import '../../utils/network_utils.dart';

// UI imports
import '../theme/app_theme.dart';
import 'widgets/settings_screen_widgets.dart';
import '../../providers/user_provider.dart';
import '../../main.dart' show appLocale;

// =======================================================
// = Function : SettingsScreen
// = Description : Modern settings screen with LavaMailTheme integration
// =======================================================
class SettingsScreen extends StatefulWidget {
  final GoogleSignInAccount user;
  final VoidCallback? onDataChanged;

  const SettingsScreen({super.key, required this.user, this.onDataChanged});

  @override
  State<SettingsScreen> createState() => _SettingsScreenState();
}

// =======================================================
// = Function : _SettingsScreenState
// = Description : State management for comprehensive settings with theme integration
// =======================================================
class _SettingsScreenState extends State<SettingsScreen> {
  // All settings state variables
  int _syncFrequency = 15;
  AppNetworkType _networkType = AppNetworkType.wifi;
  AppSyncMode _syncMode = AppSyncMode.auto;
  bool _notificationsEnabled = true;
  int _autoLogoutMinutes = 0;
  int _autoRefreshMinutes = 0; // 0 = jamais par défaut
  DataMode _currentDataMode = DataMode.online; // Online par défaut
  String _currentLanguage = 'en';
  bool _isProcessing = false;
  String _currentNetworkStatus = 'Unknown';

  // Options lists
  final List<int> _autoLogoutOptions = [0, 5, 15, 30, 60, 180, 360, 720, 1440, 2880];
  final List<int> _syncFrequencies = [5, 15, 30, 60, 120, 360, 720, 1440];
  // Global sync state
  late Stream<SyncStateInfo> _syncStateStream;

  // Labels maps
  final Map<int, String> _syncFrequencyLabels = {
    5: '5 min',
    15: '15 min',
    30: '30 min',
    60: '1 hour',
    120: '2 hours',
    360: '6 hours',
    720: '12 hours',
    1440: '24 hours',
  };

  final Map<String, String> _languageLabels = {
    'en': 'English',
    'fr': 'Français',
    'es': 'Español',
    'de': 'Deutsch',
    'hi': 'हिन्दी (Hindi)',
    'id': 'Bahasa Indonesia',
    'it': 'Italiano',
    'ja': '日本語 (Japanese)',
    'ko': '한국어 (Korean)',
    'pt': 'Português',
  };

  // Helper methods
  String _getAutoLogoutLabel(int minutes, BuildContext context) {
    final l10n = AppLocalizations.of(context);
    switch (minutes) {
      case 0: return l10n.autoLogoutNever;
      case 5: return l10n.autoLogout5min;
      case 15: return l10n.autoLogout15min;
      case 30: return l10n.autoLogout30min;
      case 60: return l10n.autoLogout1hour;
      case 180: return l10n.autoLogout3hours;
      case 360: return l10n.autoLogout6hours;
      case 720: return l10n.autoLogout12hours;
      case 1440: return l10n.autoLogout24hours;
      case 2880: return l10n.autoLogout48hours;
      default: return '$minutes minutes';
    }
  }

  String _getDataModeDisplayName(DataMode mode) {
    final l10n = AppLocalizations.of(context);
    return mode == DataMode.offline ? l10n.dataModeOffline : l10n.dataModeOnline;
  }

  String _getDataModeDescription(DataMode mode) {
    final l10n = AppLocalizations.of(context);
    return mode == DataMode.offline ? l10n.dataModeOfflineDescription : l10n.dataModeOnlineDescription;
  }

  @override
  void initState() {
    super.initState();
    _loadAllSettings();
    _initializeSyncStateStream();
    _loadNetworkStatus();
  }

  void _initializeSyncStateStream() {
    _syncStateStream = GlobalSyncStateManager.getStateStream(widget.user.email);
    _syncStateStream.listen((state) {
      if (mounted) {
        _updateUIFromSyncState(state);
      }
    });
  }

  // Load all settings from storage
  Future<void> _loadAllSettings() async {
    try {
      _syncFrequency = await AppSettings.getSyncFrequency();
      _networkType = await AppSettings.getNetworkType();
      _syncMode = await AppSettings.getSyncMode();
      _notificationsEnabled = await AppSettings.getNotificationsEnabled();
      _autoLogoutMinutes = await AppSettings.getAutoLogout();
      _autoRefreshMinutes = 0; // TODO: Implement auto refresh setting in AppSettings
      _currentDataMode = await AppSettings.getDataMode();
      _currentLanguage = await AppSettings.getLanguage();
      setState(() {});
    } catch (e) {
      debugPrint('Error loading settings: $e');
    }
  }

  // Load current network status
  Future<void> _loadNetworkStatus() async {
    try {
      _currentNetworkStatus = await NetworkUtils.getNetworkDisplayName();
      if (mounted) {
        setState(() {});
      }
    } catch (e) {
      debugPrint('Error loading network status: $e');
    }
  }

  void _updateUIFromSyncState(SyncStateInfo state) {
    switch (state.state) {
      case GlobalSyncState.idle:
        _isProcessing = false;
        break;
      case GlobalSyncState.inProgress:
        _isProcessing = true;
        break;
      case GlobalSyncState.completed:
        _isProcessing = false;
        break;
      case GlobalSyncState.error:
        _isProcessing = false;
        break;
      case GlobalSyncState.rateLimited:
        _isProcessing = false;
        break;
    }
  }

  @override
  void dispose() {
    GlobalSyncStateManager.dispose(widget.user.email);
    super.dispose();
  }

  // Save individual settings
  Future<void> _saveSetting<T>(Future<void> Function(T) setter, T value) async {
    try {
      await setter(value);
      if (widget.onDataChanged != null) widget.onDataChanged!();
    } catch (e) {
      debugPrint('Error saving setting: $e');
    }
  }

  // Save language setting and update global locale
  Future<void> _saveLanguageSetting(String languageCode) async {
    try {
      await AppSettings.setLanguage(languageCode);
      // Mettre à jour la locale globale pour changer immédiatement la langue de l'app
      appLocale.value = Locale(languageCode);
      if (widget.onDataChanged != null) widget.onDataChanged!();
    } catch (e) {
      debugPrint('Error saving language setting: $e');
    }
  }

  void _signOutAndRedirect() async {
    await GoogleSignIn().signOut();
    if (!mounted) return;
    Provider.of<UserProvider>(context, listen: false).clearUser();
    Navigator.of(context).pushNamedAndRemoveUntil('/login', (route) => false);
  }

  /// Performs manual synchronization using the global sync state manager
  Future<void> _performManualSync() async {
    setState(() {
      _isProcessing = true;
    });

    try {
      // Use the global sync state manager
      await GlobalSyncStateManager.startManualSync(widget.user, (
        SyncProgress progress,
      ) {
        // Progress callback - could be used for UI updates if needed
      }, fullSync: false);

      if (!mounted) return;
      final l10nAfter = AppLocalizations.of(context);
      final scaffoldMessenger = ScaffoldMessenger.of(context);
      setState(() {
        _isProcessing = false;
      });

      // Show success message in current language
      scaffoldMessenger.showSnackBar(
        SnackBar(
          content: Text(l10nAfter.synchronizationCompleted),
          backgroundColor: Colors.green,
          duration: const Duration(seconds: 3),
        ),
      );

      // Notify parent widget that data changed
      if (widget.onDataChanged != null) {
        widget.onDataChanged!();
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isProcessing = false;
        });

        // Show error message
        final l10nAfter = AppLocalizations.of(context);
        final scaffoldMessenger = ScaffoldMessenger.of(context);
        String errorMessage;
        if (e is SyncRateLimitException) {
          final remainingMinutes =
              e.remainingTime.inMinutes +
              (e.remainingTime.inSeconds % 60 > 0 ? 1 : 0);
          errorMessage = l10nAfter.syncRateLimitShort(remainingMinutes);
        } else {
          errorMessage = '${l10nAfter.synchronizationFailed}: ${e.toString()}';
        }

        scaffoldMessenger.showSnackBar(
          SnackBar(
            content: Text(errorMessage),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 5),
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context);

    return Theme(
      data: LavaMailTheme.lightTheme(),
      child: Scaffold(
        backgroundColor: LavaMailTheme.backgroundColor,
        appBar: AppBar(
          title: Text(l10n.settings),
          backgroundColor: LavaMailTheme.primaryColor,
          foregroundColor: Colors.white,
          elevation: 2,
          actions: [
            // Manual Sync seulement en mode Offline
            if (_currentDataMode == DataMode.offline)
              IconButton(
                icon: const Icon(Icons.refresh),
                onPressed: _isProcessing ? null : () => _performManualSync(),
                tooltip: l10n.manualSyncTooltip,
              ),
          ],
        ),
        body: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
              colors: [
                LavaMailTheme.backgroundColor,
                LavaMailTheme.backgroundColor.withValues(alpha: 0.8),
              ],
            ),
          ),
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(LavaMailTheme.spacingL),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                _buildDataModeSection(),
                const SizedBox(height: LavaMailTheme.spacingL),
                // Sync Settings seulement en mode Offline
                if (_currentDataMode == DataMode.offline) ...[
                  _buildSyncSettingsSection(),
                  const SizedBox(height: LavaMailTheme.spacingL),
                ],
                _buildNetworkSettingsSection(),
                const SizedBox(height: LavaMailTheme.spacingL),
                _buildNotificationSettingsSection(),
                const SizedBox(height: LavaMailTheme.spacingL),
                _buildLanguageSection(),
                const SizedBox(height: LavaMailTheme.spacingL),
                _buildSecuritySection(),
                const SizedBox(height: LavaMailTheme.spacingL),
                // Auto Refresh seulement en mode Online
                if (_currentDataMode == DataMode.online) ...[
                  _buildAutoRefreshSection(),
                  const SizedBox(height: LavaMailTheme.spacingL),
                ],
                _buildActionButtons(),
              ],
            ),
          ),
        ),
      ),
    );
  }

  // UI Building Methods

  Widget _buildDataModeSection() {
    final l10n = AppLocalizations.of(context);
    return _buildSettingsCard(
      title: l10n.dataMode,
      icon: Icons.cloud,
      children: [
        Text(
          '${l10n.currentMode}: ${_getDataModeDisplayName(_currentDataMode)}',
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
            color: LavaMailTheme.textSecondaryColor,
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(height: LavaMailTheme.spacingM),
        ...DataMode.values.map((mode) => RadioListTile<DataMode>(
          title: Text(_getDataModeDisplayName(mode)),
          subtitle: Text(_getDataModeDescription(mode)),
          value: mode,
          groupValue: _currentDataMode,
          activeColor: LavaMailTheme.primaryColor,
          onChanged: (DataMode? value) {
            if (value != null) {
              setState(() => _currentDataMode = value);
              _saveSetting(AppSettings.setDataMode, value);
              // Redémarrer ou arrêter le service de rafraîchissement automatique selon le mode
              _updateAutoRefreshService();
            }
          },
        )),
      ],
    );
  }

  Widget _buildSettingsCard({required String title, required IconData icon, required List<Widget> children}) {
    return Card(
      elevation: 4,
      shadowColor: LavaMailTheme.cardShadow.first.color,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(LavaMailTheme.borderRadiusS)),
      child: Padding(
        padding: const EdgeInsets.all(LavaMailTheme.spacingL),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(icon, color: LavaMailTheme.primaryColor, size: 24),
                const SizedBox(width: LavaMailTheme.spacingM),
                Expanded(
                  child: Text(
                    title,
                    style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                      color: LavaMailTheme.textPrimaryColor,
                      fontWeight: FontWeight.bold,
                    ),
                    overflow: TextOverflow.ellipsis,
                    maxLines: 1,
                  ),
                ),
              ],
            ),
            const SizedBox(height: LavaMailTheme.spacingM),
            ...children,
          ],
        ),
      ),
    );
  }

  Widget _buildSyncSettingsSection() {
    final l10n = AppLocalizations.of(context);
    return _buildSettingsCard(
      title: l10n.syncSettings,
      icon: Icons.sync,
      children: [
        SwitchListTile(
          title: Text(l10n.autoSync),
          subtitle: Text(l10n.automaticallySyncEmails),
          value: _syncMode == AppSyncMode.auto,
          activeColor: LavaMailTheme.primaryColor,
          onChanged: (bool value) {
            setState(() => _syncMode = value ? AppSyncMode.auto : AppSyncMode.manual);
            _saveSetting(AppSettings.setSyncMode, _syncMode);
          },
        ),
        const Divider(),
        ListTile(
          title: Text(l10n.syncFrequency),
          subtitle: Text(_syncFrequencyLabels[_syncFrequency] ?? '$_syncFrequency min'),
          trailing: DropdownButton<int>(
            value: _syncFrequency,
            items: _syncFrequencies.map((freq) => DropdownMenuItem(
              value: freq,
              child: Text(_syncFrequencyLabels[freq] ?? '$freq min'),
            )).toList(),
            onChanged: (int? value) {
              if (value != null) {
                setState(() => _syncFrequency = value);
                _saveSetting(AppSettings.setSyncFrequency, value);
              }
            },
          ),
        ),
      ],
    );
  }

  Widget _buildNetworkSettingsSection() {
    final l10n = AppLocalizations.of(context);
    return _buildSettingsCard(
      title: l10n.networkSettings,
      icon: Icons.network_check,
      children: [
        Text('${l10n.preferredNetworkType}:'),
        const SizedBox(height: LavaMailTheme.spacingM),
        NetworkTypeRadioGroup(
          selectedType: _networkType,
          onChanged: (AppNetworkType value) {
            setState(() => _networkType = value);
            _saveSetting(AppSettings.setNetworkType, value);
            // Refresh network status when changing to auto mode
            if (value == AppNetworkType.any) {
              _loadNetworkStatus();
            }
          },
        ),
        // Show current network status when Auto is selected
        if (_networkType == AppNetworkType.any) ...[
          const SizedBox(height: LavaMailTheme.spacingM),
          Container(
            padding: const EdgeInsets.all(LavaMailTheme.spacingM),
            decoration: BoxDecoration(
              color: LavaMailTheme.primaryColor.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(LavaMailTheme.borderRadiusS),
              border: Border.all(
                color: LavaMailTheme.primaryColor.withValues(alpha: 0.3),
              ),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.info_outline,
                  color: LavaMailTheme.primaryColor,
                  size: 20,
                ),
                const SizedBox(width: LavaMailTheme.spacingS),
                Expanded(
                  child: Text(
                    '${l10n.currentlyUsing}: $_currentNetworkStatus',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: LavaMailTheme.primaryColor,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
                IconButton(
                  icon: const Icon(Icons.refresh, size: 18),
                  onPressed: _loadNetworkStatus,
                  color: LavaMailTheme.primaryColor,
                  tooltip: l10n.refreshNetworkStatus,
                ),
              ],
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildNotificationSettingsSection() {
    final l10n = AppLocalizations.of(context);
    return _buildSettingsCard(
      title: l10n.notifications,
      icon: Icons.notifications,
      children: [
        SwitchListTile(
          title: Text(l10n.enableNotifications),
          subtitle: Text(l10n.receiveNotifications),
          value: _notificationsEnabled,
          activeColor: LavaMailTheme.primaryColor,
          onChanged: (bool value) {
            setState(() => _notificationsEnabled = value);
            _saveSetting(AppSettings.setNotificationsEnabled, value);
          },
        ),
      ],
    );
  }

  Widget _buildLanguageSection() {
    final l10n = AppLocalizations.of(context);
    return _buildSettingsCard(
      title: l10n.language,
      icon: Icons.language,
      children: [
        DropdownButtonFormField<String>(
          value: _currentLanguage,
          decoration: InputDecoration(
            labelText: l10n.selectLanguage,
            border: OutlineInputBorder(),
          ),
          items: _languageLabels.entries.map((entry) => DropdownMenuItem(
            value: entry.key,
            child: Text(entry.value),
          )).toList(),
          onChanged: (String? value) {
            if (value != null) {
              setState(() => _currentLanguage = value);
              _saveLanguageSetting(value);
            }
          },
        ),
      ],
    );
  }

  Widget _buildSecuritySection() {
    final l10n = AppLocalizations.of(context);
    return _buildSettingsCard(
      title: l10n.security,
      icon: Icons.security,
      children: [
        ListTile(
          title: Text(l10n.autoLogout),
          subtitle: Text(_getAutoLogoutLabel(_autoLogoutMinutes, context)),
          trailing: DropdownButton<int>(
            value: _autoLogoutMinutes,
            items: _autoLogoutOptions.map((minutes) => DropdownMenuItem(
              value: minutes,
              child: Text(_getAutoLogoutLabel(minutes, context)),
            )).toList(),
            onChanged: (int? value) {
              if (value != null) {
                setState(() => _autoLogoutMinutes = value);
                _saveSetting(AppSettings.setAutoLogout, value);
              }
            },
          ),
        ),
      ],
    );
  }

  // Méthode pour mettre à jour le service de rafraîchissement automatique selon le mode
  void _updateAutoRefreshService() {
    // Cette méthode notifie l'écran principal du changement de mode
    // Le service sera redémarré automatiquement dans home_screen.dart
    if (widget.onDataChanged != null) {
      widget.onDataChanged!();
    }
  }

  Widget _buildAutoRefreshSection() {
    final l10n = AppLocalizations.of(context);
    final List<int> autoRefreshOptions = [0, 5, 15, 30, 60, 120, 360, 720, 1440];

    String getAutoRefreshLabel(int minutes) {
      switch (minutes) {
        case 0: return l10n.autoRefreshNever;
        case 5: return l10n.autoRefresh5min;
        case 15: return l10n.autoRefresh15min;
        case 30: return l10n.autoRefresh30min;
        case 60: return l10n.autoRefresh1hour;
        case 120: return l10n.autoRefresh2hours;
        case 360: return l10n.autoRefresh6hours;
        case 720: return l10n.autoRefresh12hours;
        case 1440: return l10n.autoRefresh24hours;
        default: return '$minutes minutes';
      }
    }

    return _buildSettingsCard(
      title: l10n.autoRefresh,
      icon: Icons.refresh,
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(vertical: 4.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                l10n.autoRefresh,
                style: Theme.of(context).textTheme.titleMedium,
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
              const SizedBox(height: 4),
              Text(
                l10n.autoRefreshDescription,
                style: Theme.of(context).textTheme.bodySmall,
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
                softWrap: true,
              ),
              const SizedBox(height: 8),
              DropdownButton<int>(
                value: _autoRefreshMinutes,
                isExpanded: true,
                items: autoRefreshOptions.map((minutes) => DropdownMenuItem(
                  value: minutes,
                  child: Text(getAutoRefreshLabel(minutes)),
                )).toList(),
                onChanged: (int? value) {
                  if (value != null) {
                    setState(() => _autoRefreshMinutes = value);
                    // TODO: Implement auto refresh setting in AppSettings
                    // _saveSetting(AppSettings.setAutoRefresh, value);
                    // Redémarrer le service de rafraîchissement automatique avec la nouvelle valeur
                    _updateAutoRefreshService();
                  }
                },
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildActionButtons() {
    final l10n = AppLocalizations.of(context);
    return Column(
      children: [
        // Manual Sync seulement en mode Offline
        if (_currentDataMode == DataMode.offline) ...[
          SizedBox(
            width: double.infinity,
            child: ElevatedButton.icon(
              onPressed: _isProcessing ? null : _performManualSync,
              icon: _isProcessing ? const SizedBox(width: 20, height: 20, child: CircularProgressIndicator(strokeWidth: 2)) : const Icon(Icons.sync),
              label: Text(_isProcessing ? l10n.syncing : l10n.manualSync),
              style: ElevatedButton.styleFrom(
                backgroundColor: LavaMailTheme.primaryColor,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: LavaMailTheme.spacingM),
                shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(LavaMailTheme.borderRadiusS)),
              ),
            ),
          ),
          const SizedBox(height: LavaMailTheme.spacingM),
        ],
        SizedBox(
          width: double.infinity,
          child: OutlinedButton.icon(
            onPressed: _signOutAndRedirect,
            icon: const Icon(Icons.logout),
            label: Text(l10n.signOut),
            style: OutlinedButton.styleFrom(
              foregroundColor: LavaMailTheme.errorColor,
              side: const BorderSide(color: LavaMailTheme.errorColor),
              padding: const EdgeInsets.symmetric(vertical: LavaMailTheme.spacingM),
              shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(LavaMailTheme.borderRadiusS)),
            ),
          ),
        ),
      ],
    );
  }
}
